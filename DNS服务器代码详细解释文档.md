# DNS服务器代码详细解释文档

## 📋 文档说明
本文档详细解释了DNS中继服务器项目中的关键代码段，包括命令行参数解析、网络初始化等核心功能的实现原理。

---

## 🔧 第一部分：命令行参数解析 (`setConfigure`函数)

### 代码位置：`src/system.c`

```c
void setConfigure(int argc, char* argv[]) {
    argc--;
    for (int index = 1; index <= argc; index++) {
        // 调试模式
        if (strcmp(argv[index], "-d") == 0) {
            debug_mode = 1;
        }

        // 日志模式
        if (strcmp(argv[index], "-l") == 0) {
            log_mode = 1;
        }

        //输出基本信息 
        else if (strcmp(argv[index], "-i") == 0) {
            printf("far addr: %s ( 10.3.9.45, BUPT DNS) \n", remote_dns);
        }

        //设置远程DNS服务器
        else if (strcmp(argv[index], "-s") == 0) {
            char* addr = malloc(16);
            memset(addr, 0, 16);
            index++;
            memcpy(addr, argv[index], strlen(argv[index]) + 1);
            remote_dns = addr;
        }
    }
}
```

### 1. argc、argv参数详解

#### 参数传递路径
```c
// main.c
int main(int argc, char *argv[]){
    init(argc, argv);    // 传递给init函数
    // ...
}

// system.c  
void init(int argc, char* argv[]) {
    setConfigure(argc, argv);    // 再传递给setConfigure函数
    // ...
}
```

#### 参数含义和作用

**argc (argument count)**：
- **含义**：命令行参数的数量（包括程序名本身）
- **类型**：`int`
- **作用**：告诉程序有多少个命令行参数

**argv (argument vector)**：
- **含义**：指向字符串数组的指针，存储所有命令行参数
- **类型**：`char*[]` 或 `char**`
- **作用**：存储具体的命令行参数内容

#### 实际使用示例
```bash
# 命令行输入
./dns_server -d -s *******

# 对应的argc和argv值：
argc = 4
argv[0] = "./dns_server"    // 程序名
argv[1] = "-d"              // 第一个参数
argv[2] = "-s"              // 第二个参数  
argv[3] = "*******"         // 第三个参数
argv[4] = NULL              // 数组结束标志
```

### 2. strcmp函数详解

#### 函数原型和作用
```c
int strcmp(const char *str1, const char *str2);
```

**作用**：比较两个字符串是否相等

#### 返回值含义
- **返回0**：两个字符串完全相同
- **返回负数**：str1 < str2（按字典序）
- **返回正数**：str1 > str2（按字典序）

#### 在项目中的使用
```c
if (strcmp(argv[index], "-d") == 0) {
    debug_mode = 1;
}
```

**解释**：
- `argv[index]`：当前处理的命令行参数
- `"-d"`：要比较的目标字符串
- `== 0`：判断是否完全相等

#### strcmp使用示例
```c
// 示例1：相等情况
char* arg = "-d";
if (strcmp(arg, "-d") == 0) {
    printf("匹配成功！\n");  // 会执行
}

// 示例2：不相等情况
char* arg = "-debug";
if (strcmp(arg, "-d") == 0) {
    printf("不会执行\n");  // 不会执行
}

// 示例3：字典序比较
strcmp("apple", "banana");  // 返回负数（apple < banana）
strcmp("zebra", "apple");   // 返回正数（zebra > apple）
```

### 3. malloc函数详解

#### 函数原型和作用
```c
void* malloc(size_t size);
```

**作用**：在堆内存中分配指定字节数的连续内存空间

#### malloc(16)的含义
```c
char* addr = malloc(16);  // 分配16字节（不是16比特！）
```

**重要说明**：
- **16字节**，不是16比特
- 1字节 = 8比特，所以16字节 = 128比特
- 足够存储IPv4地址字符串（如"***************"需要15个字符+1个结束符）

#### malloc使用详解
```c
// 基本使用
char* ptr = malloc(16);  // 分配16字节内存
if (ptr == NULL) {
    // 内存分配失败处理
    printf("内存分配失败！\n");
    exit(1);
}

// 使用内存
strcpy(ptr, "*******");

// 释放内存（重要！）
free(ptr);
ptr = NULL;  // 避免悬空指针
```

### 4. memset函数详解

#### 函数原型和作用
```c
void* memset(void* ptr, int value, size_t num);
```

**作用**：将内存区域的每个字节设置为指定值

#### 参数说明
- **ptr**：要设置的内存区域指针
- **value**：要设置的值（通常是0）
- **num**：要设置的字节数

#### 在项目中的使用
```c
char* addr = malloc(16);
memset(addr, 0, 16);  // 将16字节全部设置为0
```

**作用**：确保新分配的内存是"干净"的，避免垃圾数据

#### memset使用示例
```c
// 示例1：清零内存
char buffer[100];
memset(buffer, 0, sizeof(buffer));  // 全部设置为0

// 示例2：初始化结构体
struct sockaddr_in addr;
memset(&addr, 0, sizeof(addr));  // 清零整个结构体

// 示例3：设置特定值
char array[10];
memset(array, 'A', 10);  // 全部设置为字符'A'
```

### 5. memcpy函数详解

#### 函数原型和作用
```c
void* memcpy(void* dest, const void* src, size_t num);
```

**作用**：将源内存区域的数据复制到目标内存区域

#### 参数说明
- **dest**：目标内存地址
- **src**：源内存地址  
- **num**：要复制的字节数

#### 在项目中的使用
```c
memcpy(addr, argv[index], strlen(argv[index]) + 1);
```

**详细解释**：
- **dest**: `addr` - 新分配的16字节内存
- **src**: `argv[index]` - 命令行参数中的IP地址字符串
- **num**: `strlen(argv[index]) + 1` - 字符串长度+1（包含结束符'\0'）

#### memcpy vs strcpy的区别
```c
// strcpy：专门用于字符串复制，自动处理结束符
strcpy(dest, src);

// memcpy：通用内存复制，需要指定字节数
memcpy(dest, src, strlen(src) + 1);  // +1是为了包含'\0'
```

---

## 🌐 第二部分：网络初始化 (`initSocket`函数)

### 代码位置：`src/system.c`

```c
void initSocket(int port) {
    //初始化，否则无法运行socket
    WORD wVersion = MAKEWORD(2, 2);
    WSADATA wsadata;
    if (WSAStartup(wVersion, &wsadata) != 0) {
        return;
    }

    client_sock = socket(AF_INET, SOCK_DGRAM, 0);
    server_sock = socket(AF_INET, SOCK_DGRAM, 0);

    //初始化两个结构体以留空
    memset(&client_addr, 0, sizeof(client_addr));
    memset(&server_addr, 0, sizeof(server_addr));

    client_addr.sin_family = AF_INET;
    client_addr.sin_addr.s_addr = INADDR_ANY;     //INADDR_ANY表示本机的任意IP地址
    client_addr.sin_port = htons(port);

    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = inet_addr(remote_dns);      //远程主机地址
    server_addr.sin_port = htons(port);

    // 端口复用
    const int REUSE = 1;
    setsockopt(client_sock, SOL_SOCKET, SO_REUSEADDR, (const char*)&REUSE, sizeof(REUSE));

    if (bind(client_sock, (SOCKADDR*)&client_addr, addr_len) < 0) {
        printf("ERROR: Could not bind: %s\n", strerror(errno));
        exit(-1);
    }

    char* DNS_server = remote_dns;
    printf("\nDNS server: %s\n", DNS_server);
    printf("Listening on port %d\n\n", port);
}
```

### 1. 为什么需要bind()？Socket监听是什么？

#### Socket的本质理解
想象Socket就像**邮局的邮箱**：

```c
// 创建Socket就像申请了一个邮箱
client_sock = socket(AF_INET, SOCK_DGRAM, 0);
// 此时：邮箱存在，但没有地址，别人无法给你寄信
```

#### bind()的作用
```c
// bind()就像给邮箱分配一个具体地址
bind(client_sock, (SOCKADDR*)&client_addr, addr_len);
// 此时：邮箱有了地址"本机IP:53端口"，别人可以给你寄信了
```

#### 详细对比
| 操作       | Socket状态   | 类比                 | 能否接收数据 |
| ---------- | ------------ | -------------------- | ------------ |
| `socket()` | 创建了Socket | 申请了邮箱，但没地址 | ❌ 不能       |
| `bind()`   | 绑定了地址   | 邮箱有了具体地址     | ✅ 可以       |

### 2. 绑定Socket到端口的详细流程

#### 绑定前后的系统状态变化

**绑定前的状态**：
```
操作系统的端口表：
端口53: [空闲] ← 没有程序监听
端口80: [被Apache占用]
端口22: [被SSH占用]

你的程序：
client_sock: [存在但无地址] ← Socket存在但系统不知道它要监听哪个端口
```

**绑定后的状态**：
```
操作系统的端口表：
端口53: [被你的DNS程序占用] ← 系统知道53端口的数据要给你的程序
端口80: [被Apache占用]
端口22: [被SSH占用]

你的程序：
client_sock: [绑定到0.0.0.0:53] ← Socket有了明确的地址
```

#### bind()系统调用的内部过程
```c
// 当你调用bind()时，操作系统内部发生：

1. 检查端口53是否已被占用
   if (端口53被占用) {
       return "Address already in use";
   }

2. 检查权限（53是特权端口，需要管理员权限）
   if (端口 < 1024 && 不是管理员) {
       return "Permission denied";
   }

3. 在系统端口表中注册
   端口表[53] = {
       socket: client_sock,
       进程ID: 当前进程ID,
       协议: UDP
   };

4. 返回成功
   return 0;
```

### 3. 网络架构图解详细说明

#### 完整的数据流向图
```
客户端程序                DNS中继服务器                上游DNS服务器
(如nslookup)              (你的程序)                   (如*******)

┌─────────────┐          ┌─────────────────────┐      ┌─────────────┐
│*************│          │    你的服务器        │      │   *******   │
│   :12345    │          │                     │      │    :53      │
└─────────────┘          └─────────────────────┘      └─────────────┘
       │                           │                         │
       │ ①DNS查询                   │                         │
       │ 目标:服务器IP:53            │                         │
       └──────────────────────────→│                         │
                                  │ client_sock              │
                                  │ (绑定到0.0.0.0:53)       │
                                  │ 接收客户端查询            │
                                  │                         │
                                  │ ②转发查询                │
                                  │ server_sock ────────────→│
                                  │ (连接到*******:53)      │
                                  │                         │
                                  │ ③接收响应                │
                                  │ server_sock ←────────────│
                                  │                         │
       ┌──────────────────────────│ ④返回响应                │
       │                          │ client_sock              │
       ←──────────────────────────│                         │
       │                          │                         │
```

#### 两个Socket的具体作用

**client_sock（监听Socket）**：
```c
// 配置：监听所有网卡的53端口
client_addr.sin_addr.s_addr = INADDR_ANY;  // 0.0.0.0
client_addr.sin_port = htons(53);          // 端口53

// 作用：
// 1. 接收来自客户端的DNS查询
// 2. 发送DNS响应给客户端
// 3. 必须bind()到53端口，因为客户端期望DNS服务器监听53端口
```

**server_sock（客户端Socket）**：
```c
// 配置：连接到上游DNS服务器
server_addr.sin_addr.s_addr = inet_addr("*******");  // 上游DNS IP
server_addr.sin_port = htons(53);                     // 上游DNS端口

// 作用：
// 1. 向上游DNS服务器发送查询
// 2. 接收上游DNS服务器的响应
// 3. 不需要bind()到特定端口，系统会自动分配端口
```

#### 为什么需要两个Socket？

**如果只用一个Socket会怎样？**
```c
// 错误的设计：只用一个Socket
SOCKET only_sock = socket(AF_INET, SOCK_DGRAM, 0);

// 问题1：端口冲突
bind(only_sock, 本机:53);  // 监听客户端
// 但是向*******发送数据时，源端口也是53
// 这会导致*******的响应可能被误认为是客户端的新查询

// 问题2：数据混乱
// 无法区分收到的数据是来自客户端还是上游DNS服务器
```

**正确的双Socket设计：**
```c
// client_sock：专门处理客户端通信
bind(client_sock, 本机:53);     // 固定监听53端口
// 只接收客户端查询，只发送响应给客户端

// server_sock：专门处理上游DNS通信  
// 不需要bind，系统自动分配端口（如54321）
// 只向上游DNS发送查询，只接收上游DNS响应
```

这样设计的好处：
1. **职责分离**：每个Socket有明确的作用
2. **避免冲突**：不同的端口处理不同的通信
3. **易于管理**：代码逻辑更清晰

---

## 🚀 第三部分：DNS服务器核心逻辑 (`server.c`文件)

### 代码位置：`src/server.c`

这个文件是DNS中继服务器的核心，包含了事件循环、客户端请求处理、上游DNS通信等关键功能。

### 1. 全局变量和初始化

```c
#include "../head/server.h"

int addr_len = sizeof(struct sockaddr_in);
char* remote_dns = "10.3.9.5";  // 上游DNS服务器地址
```

**说明**：
- `addr_len`：Socket地址结构体的大小，用于网络函数调用
- `remote_dns`：默认的上游DNS服务器地址（可通过命令行参数修改）

### 2. 事件驱动的主循环 (`poll`函数)

```c
void poll() {
    u_long block_mode = 1;
    int server_result = ioctlsocket(server_sock, FIONBIO, &block_mode);
    int client_result = ioctlsocket(client_sock, FIONBIO, &block_mode);

    if (server_result == SOCKET_ERROR || client_result == SOCKET_ERROR) {
        printf("ioctlsocket failed with error: %d\n", WSAGetLastError());
        closesocket(server_sock);
        closesocket(client_sock);
        WSACleanup();
        return 1;
    }

    struct pollfd fds[2];

    while (1) {
        timeout_handle();
        fds[0].fd = client_sock;
        fds[0].events = POLLIN;
        fds[1].fd = server_sock;
        fds[1].events = POLLIN;

        int ret = WSAPoll(fds, 2, 5);
        if (ret == SOCKET_ERROR) {
            printf("ERROR WSAPoll: %d.\n", WSAGetLastError());
        }
        else if (ret > 0) {
            if (fds[0].revents & POLLIN) {
                receiveClient();
            }
            if (fds[1].revents & POLLIN) {
                receiveServer();
            }
        }
    }
}
```

#### 详细解析

**非阻塞模式设置**：
```c
u_long block_mode = 1;
ioctlsocket(server_sock, FIONBIO, &block_mode);
ioctlsocket(client_sock, FIONBIO, &block_mode);
```

**作用**：
- `FIONBIO`：设置Socket为非阻塞模式
- `block_mode = 1`：启用非阻塞模式
- **好处**：Socket操作不会阻塞程序，可以同时处理多个连接

**事件轮询机制**：
```c
struct pollfd fds[2];
fds[0].fd = client_sock;    // 监听客户端Socket
fds[0].events = POLLIN;     // 监听可读事件
fds[1].fd = server_sock;    // 监听服务器Socket
fds[1].events = POLLIN;     // 监听可读事件

int ret = WSAPoll(fds, 2, 5);  // 轮询，超时5毫秒
```

**WSAPoll参数说明**：
- `fds`：要监听的文件描述符数组
- `2`：监听2个Socket
- `5`：超时时间5毫秒

**事件处理逻辑**：
```c
if (ret > 0) {  // 有事件发生
    if (fds[0].revents & POLLIN) {  // 客户端有数据
        receiveClient();
    }
    if (fds[1].revents & POLLIN) {  // 服务器有数据
        receiveServer();
    }
}
```

### 3. 超时处理机制 (`timeout_handle`函数)

```c
void timeout_handle() {
    for (int i = 0; i < ID_LIST_SIZE; i++) {
        if (ID_list[i].expire_time < time(NULL) && ID_list[i].expire_time != 0) {
            uint8_t buffer[BUFFER_SIZE];
            setDNSMessage(ID_list[i].msg, buffer, NULL, 0);
            uint16_t old_ID = htons(ID_list[i].client_ID);
            memcpy(buffer, &old_ID, sizeof(uint16_t));
            sendto(client_sock, buffer, ID_list[i].msg_size, 0,
                   (struct sockaddr*)&ID_list[i].client_addr, addr_len);

            if (debug_mode == 1) {
                time_t timep;
                time(&timep);
                printf("%s", ctime(&timep));
                printf("ID %d from %s:%d timeout.\n", ID_list[i].client_ID,
                       inet_ntoa(ID_list[i].client_addr.sin_addr),
                       ntohs(ID_list[i].client_addr.sin_port));
            }

            // 清空该ID的信息
            ID_list[i].client_ID = 0;
            ID_list[i].expire_time = 0;
            ID_list[i].msg = NULL;
            ID_list[i].msg_size = 0;
            memset(&(ID_list[i].client_addr), 0, sizeof(struct sockaddr_in));
        }
    }
}
```

#### 超时处理的作用

**为什么需要超时处理？**
```
客户端发送DNS查询 → 中继服务器 → 上游DNS服务器
                                    ↓
                              如果上游DNS无响应？
                                    ↓
                            客户端会一直等待...
```

**超时处理流程**：
1. **检查过期**：`ID_list[i].expire_time < time(NULL)`
2. **构造错误响应**：`setDNSMessage(ID_list[i].msg, buffer, NULL, 0)`
3. **恢复原始ID**：`memcpy(buffer, &old_ID, sizeof(uint16_t))`
4. **发送给客户端**：告知查询失败
5. **清理资源**：释放ID映射表项

### 4. 客户端请求处理 (`receiveClient`函数)

```c
void receiveClient() {
    uint8_t buffer[BUFFER_SIZE];        // 接收的报文
    uint8_t buffer_to_client[BUFFER_SIZE];    // 回复给客户端的报文
    DNS_message msg;
    // 初始化DNS消息结构
    msg.additional = NULL;
    msg.question = NULL;
    msg.authority = NULL;
    msg.header = NULL;
    msg.answer = NULL;

    // 初始化IP地址链表
    Ips** head = (struct Ips**)malloc(sizeof(struct Ips*));
    (*head) = (struct Ips*)malloc(sizeof(struct Ips));
    (*head)->next = NULL;
    (*head)->type = 0;
    (*head)->isFilledIp4 = 0;
    (*head)->isFilledIp6 = 0;

    int msg_size = -1;
    int found_cnt = 0;

    // 接收客户端数据
    msg_size = recvfrom(client_sock, buffer, sizeof(buffer), 0,
                       (struct sockaddr*)&client_addr, &addr_len);

    if (msg_size < 0) {
        printf("ERROR: Could not receive: %s\n", strerror(errno));
        return;
    }
    else {
        uint8_t* start = buffer;
        if (debug_mode == 1) {
            printf("\n------------------DNS data------------------\n");
        }

        // 解析DNS报文
        getDNSMessage(&msg, buffer, start);

        if(debug_mode == 1){
            time_t timep;
            time(&timep);
            printf("%s", ctime(&timep));
            printf("ID %d from Client %s:%d\n", msg.header->id,
                   inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
            printf("Target Domain: %s\n\n", msg.question->q_name);
        }

        (*head)->type = msg.question->q_type;

        // 三级查找策略
        if (msg.question->q_type == RR_A || msg.question->q_type == RR_AAAA) {
            // 1. 从缓存查找
            found_cnt = search_cache(msg.question->q_name, head);

            // 2. 缓存未命中，从本地host文件查找
            if (found_cnt == 0) {
                if (debug_mode == 1) {
                    printf("Address not found in cache.\n");
                }
                found_cnt = search(msg.question->q_name, head);
            }

            if (found_cnt == 0) {
                if (debug_mode == 1) {
                    printf("Address not found in host file.\n");
                }
            }
        }

        // 3. 本地无法解析，转发到上游DNS
        if (found_cnt == 0) {
            // 放入ID映射表，获取新的ID
            uint16_t new_id = add_list_id(msg.header->id, client_addr, &msg, msg_size);
            uint16_t new_id_temp = new_id;
            if (new_id != ID_LIST_SIZE) {
                new_id = htonl(new_id);
                memcpy(buffer, &new_id, 2);
                // 发送到上游DNS服务器
                sendto(server_sock, buffer, msg_size, 0,
                       (struct sockaddr*)&server_addr, addr_len);
                if(debug_mode == 1){
                    time_t timep;
                    time(&timep);
                    printf("%s", ctime(&timep));
                    printf("relay to far dns server.\n");
                    printf("NewID: %d, OldID: %d\n\n", new_id_temp, msg.header->id);
                }
            }
            return;
        }

        // 本地找到结果，构造响应
        Address_Dns end;
        end = setDNSMessage(&msg, buffer_to_client, head, found_cnt);
        int len = end - buffer_to_client;
        sendto(client_sock, buffer_to_client, len, 0,
               (struct sockaddr*)&client_addr, addr_len);
    }
}
```

#### DNS查找的三级策略

```
客户端查询 → 1. 缓存查找 → 2. 本地host文件查找 → 3. 转发上游DNS
              ↓ 命中           ↓ 命中              ↓ 无结果
            直接返回        直接返回           等待上游响应
```

**优势**：
1. **缓存命中**：最快响应，减少网络开销
2. **本地解析**：支持自定义域名映射
3. **上游转发**：保证完整的DNS解析能力

#### ID映射机制详解

**为什么需要ID映射？**
```
问题场景：
客户端A查询www.baidu.com，ID=1234
客户端B查询www.google.com，ID=1234  ← ID冲突！

如果直接转发：
上游DNS返回两个ID=1234的响应，无法区分给哪个客户端
```

**解决方案：**
```c
// 1. 保存原始信息
uint16_t new_id = add_list_id(msg.header->id, client_addr, &msg, msg_size);

// 2. 修改报文ID
new_id = htonl(new_id);
memcpy(buffer, &new_id, 2);

// 3. 转发到上游DNS
sendto(server_sock, buffer, msg_size, 0, (struct sockaddr*)&server_addr, addr_len);
```

**映射表结构**：
```c
ID_list[0] = {client_ID: 1234, internal_ID: 0, client_addr: 客户端A地址}
ID_list[1] = {client_ID: 1234, internal_ID: 1, client_addr: 客户端B地址}
```

### 5. 上游DNS响应处理 (`receiveServer`函数)

```c
void receiveServer() {
    uint8_t buffer[BUFFER_SIZE];
    DNS_message msg;
    // 初始化DNS消息结构
    msg.additional = NULL;
    msg.question = NULL;
    msg.authority = NULL;
    msg.header = NULL;
    msg.answer = NULL;
    int msg_size = -1;

    // 接收上游DNS服务器的响应
    msg_size = recvfrom(server_sock, buffer, sizeof(buffer), 0,
                       (struct sockaddr*)&server_addr, &addr_len);

    if (msg_size > 0) {
        getDNSMessage(&msg, buffer, buffer);

        if(debug_mode == 1){
            time_t timep;
            time(&timep);
            printf("%s", ctime(&timep));
            printf("ID %d from Server %s:%d\n", msg.header->id,
                   inet_ntoa(server_addr.sin_addr), ntohs(server_addr.sin_port));
            printf("Target Domain: %s\n\n", msg.question->q_name);
        }

        // ID转换：恢复原始客户端ID
        uint16_t ID = msg.header->id;
        uint16_t old_ID = htons(ID_list[ID].client_ID);
        memcpy(buffer, &old_ID, sizeof(uint16_t));

        struct sockaddr_in ca = ID_list[ID].client_addr;

        // 清空ID映射表项
        ID_list[ID].expire_time = 0;
        ID_list[ID].client_ID = 0;
        ID_list[ID].msg = NULL;
        ID_list[ID].msg_size = 0;
        memset(&(ID_list[ID].client_addr), 0, sizeof(struct sockaddr_in));

        // 更新缓存
        if (msg.header->ans_num > 0 && (msg.answer->type == RR_A || msg.answer->type == RR_AAAA)) {
            struct Ips* update = (struct Ips*)malloc(sizeof(struct Ips));
            update->next = NULL;
            Ips* p = update;
            DNS_resource_record* q = msg.answer;

            while (q) {
                p->next = (struct Ips*)malloc(sizeof(struct Ips));
                p->next->next = NULL;
                if (q->type == RR_A) {
                    p->next->type = q->type;
                    p->next->isFilledIp4 = 1;
                    for (int i = 0; i < 4; i++) {
                        p->next->ip[i] = q->r_data.a_record.IP_addr[i];
                    }
                }
                else if (q->type == RR_AAAA) {
                    p->next->type = q->type;
                    p->next->isFilledIp6 = 1;
                    memcpy(p->next->ip6, q->r_data.aaaa_record.IP_addr, sizeof(uint8_t) * 16);
                    for (int i = 0; i < 8; i++) {
                        p->next->ip6[i] = ntohs(p->next->ip6[i]);
                    }
                }
                else {
                    p->next = NULL;
                    break;
                }
                p = p->next;
                q = q->next;
            }
            update_cache(msg.question->q_name, update);
        }

        // 更新本地host文件
        int isFound = 0;
        Ips* temp = (struct Ips*)malloc(sizeof(struct Ips));
        temp->type = msg.question->q_type;
        if (msg.answer != NULL) {
            isFound = search(msg.answer->name, &temp);
            if (isFound == 0) {
                insert_host(&msg);
            }
        }

        // 转发响应给客户端
        sendto(client_sock, buffer, msg_size, 0, (struct sockaddr*)&ca, addr_len);
    }
}
```

#### 响应处理的关键步骤

**1. ID恢复**：
```c
uint16_t ID = msg.header->id;           // 获取内部ID
uint16_t old_ID = htons(ID_list[ID].client_ID);  // 获取原始客户端ID
memcpy(buffer, &old_ID, sizeof(uint16_t));      // 恢复原始ID
```

**2. 缓存更新**：
- 将上游DNS的查询结果存入LRU缓存
- 提高后续相同查询的响应速度

**3. 本地存储更新**：
- 将新的域名-IP映射写入host文件
- 实现持久化存储

### 6. 资源清理 (`closeServer`函数)

```c
void closeServer() {
    closesocket(client_sock);
    closesocket(server_sock);
    WSACleanup();  // 释放Windows Socket资源
}
```

**作用**：程序退出时清理网络资源，避免资源泄漏。

---

## 🔧 调试示例代码

你可以添加以下调试代码来观察函数的工作过程：

```c
void setConfigure(int argc, char* argv[]) {
    printf("DEBUG: argc = %d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("DEBUG: argv[%d] = %s\n", i, argv[i]);
    }

    argc--;
    for (int index = 1; index <= argc; index++) {
        printf("DEBUG: 处理参数: %s\n", argv[index]);

        if (strcmp(argv[index], "-s") == 0) {
            char* addr = malloc(16);
            printf("DEBUG: malloc分配地址: %p\n", addr);

            memset(addr, 0, 16);
            printf("DEBUG: memset后内存已清零\n");

            index++;
            printf("DEBUG: IP地址参数: %s\n", argv[index]);

            memcpy(addr, argv[index], strlen(argv[index]) + 1);
            printf("DEBUG: memcpy后addr内容: %s\n", addr);

            remote_dns = addr;
        }
    }
}
```

---

## 📚 总结

通过本文档的详细解释，你应该能够理解：

1. **命令行参数处理**：argc/argv的含义、strcmp的使用、内存管理函数的作用
2. **网络编程基础**：Socket的创建、绑定、监听的完整流程
3. **系统架构设计**：为什么需要双Socket设计，数据流向如何工作
4. **DNS服务器核心逻辑**：事件驱动编程、三级查找策略、ID映射机制、缓存管理

这些知识是理解整个DNS服务器项目的基础，掌握后你就能更好地阅读和修改项目代码了！
